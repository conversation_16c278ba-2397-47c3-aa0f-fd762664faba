// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

abstract class SConstants {
  ///your super up base domain url
  ///like this (example.com) not start https// or any sub domains example [superupdev.com]
  static const _productionBaseUrl = "10.0.2.2:3000";
  // static const _productionBaseUrl = "192.168.1.120:3000"; // For real device
  // static const _productionBaseUrl = "10.0.2.2:3000"; // For Android emulator

  ///your app name
  static const appName = "Orbit";

  ///android and ios admob ids [https://developers.google.com/admob/flutter/quick-start] [https://developers.google.com/ad-manager/mobile-ads-sdk/flutter/quick-start]
  ///if it null it will stop showing for the banners
  static String? androidBannerAdsUnitId =
      null; //"ca-app-pub-7278227650091991/**********";
  static String? iosBannerAdsUnitId =
      null; // "ca-app-pub-5480704135551772/**********";

  ///set the Interstitial id if it null it will stop showing
  static String? androidInterstitialId = null;
  // "ca-app-pub-7278227650091991/1126623640";
  static String? iosInterstitialId =
      null; //"ca-app-pub-5480704135551772/5139727176";

  ///get from https://console.firebase.google.com/project/_/settings/cloudmessaging
  ///follow this https://firebase.flutter.dev/docs/messaging/usage/#web-tokens
  static String? webVapidKey =
      "BHP0SzkxLsXqJ3infDKq7mUIE63ML81Ag6O2b3K_Nr1QtM_WjNo2-JBXK4liurzWjiZ9WKqh9iPtA4hOhxiYgGc";

  ///setup video and voice calls [https://agora.io]
  ///update this with your agora app id
  static const agoraAppId = "d0ee11dc4ee449528974276b8ddb1c74";

  ///change this to your google maps api key to enable google maps location picker
  static const googleMapsApiKey = "AIzaSyBGhJMHg8_3OqsJy2J0DGLnEtz09yaK5Fc";

  ///set the onesignal id for push notifications [https://onesignal.com]
  ///update this with your onesignal app id  static const oneSignalAppId = "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx";
  static const oneSignalAppId = "************************************";

  ///don't update update only if you use server ip just return your server ip with port [12.xxx.xxx:80/]
  static String get baseMediaUrl {
    ///if you don't setup domain yet you can return the server ip like this [return Uri.parse("http://ip:port/");]
    // return "https://api.$_productionBaseUrl/";
    return "http://$_productionBaseUrl/";
  }

  ///don't update update only if you use server ip just return your server ip with port [12.xxx.xxx:80/api/v1]
  static Uri get sApiBaseUrl {
    ///if you don't setup domain yet you can return the server ip like this [return Uri.parse("http://ip:port/api/v1");]
    // return Uri.parse("https://api.$_productionBaseUrl/api/v1");
    return Uri.parse("http://$_productionBaseUrl/api/v1");
  }
}
